'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { useAuth } from '@/lib/auth/AuthContext';
import { jobService } from '@/api/jobService';
import { Button } from '@/components/ui';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Icon } from '@/components/ui';
import { Badge } from '@/components/ui/Badge';
import { formatDistanceToNow } from 'date-fns';
import Link from 'next/link';

interface ProposalDetails {
  id: string;
  jobId: string;
  freelancerId: string;
  coverLetter: string;
  bidAmount: number;
  proposedRate: number;
  status: string;
  createdAt: string;
  updatedAt: string;
  job?: {
    id: string;
    title: string;
    description: string;
    budget: number;
    status: string;
    isRemote: boolean;
    clientId: string;
    createdAt: string;
    client?: {
      id: string;
      name: string;
      email: string;
    };
  };
}

const ProposalDetailsPage = () => {
  const params = useParams();
  const router = useRouter();
  const { isAuthenticated, user } = useAuth();
  const [proposal, setProposal] = useState<ProposalDetails | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const proposalId = params.id as string;

  useEffect(() => {
    const fetchProposalDetails = async () => {
      if (!user?.username || !proposalId) return;

      try {
        setIsLoading(true);
        setError(null);

        // Get all proposals for the freelancer and find the specific one
        const proposals = await jobService.listMyProposals(user.username);
        const targetProposal = proposals.find(p => p.id === proposalId);

        if (!targetProposal) {
          setError('Proposal not found');
          return;
        }

        setProposal(targetProposal as ProposalDetails);
      } catch (err) {
        console.error('Error fetching proposal details:', err);
        setError('Failed to load proposal details');
      } finally {
        setIsLoading(false);
      }
    };

    if (isAuthenticated && user?.attributes?.['custom:role'] === 'FREELANCER') {
      fetchProposalDetails();
    }
  }, [isAuthenticated, user, proposalId]);

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      PENDING: { variant: 'secondary' as const, label: 'Pending', icon: 'Clock' },
      ACCEPTED: { variant: 'success' as const, label: 'Accepted', icon: 'CheckCircle' },
      REJECTED: { variant: 'destructive' as const, label: 'Rejected', icon: 'XCircle' },
    };

    const config = statusConfig[status as keyof typeof statusConfig] ||
                  { variant: 'secondary' as const, label: status, icon: 'HelpCircle' };

    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        <Icon name={config.icon as any} size="sm" />
        {config.label}
      </Badge>
    );
  };

  if (!isAuthenticated) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Icon name="Loader2" size="xl" className="animate-spin" />
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button variant="ghost" onClick={() => router.back()}>
            <Icon name="ArrowLeft" size="sm" className="mr-2" />
            Back
          </Button>
        </div>
        <div className="flex items-center justify-center py-12">
          <Icon name="Loader2" size="xl" className="animate-spin" />
        </div>
      </div>
    );
  }

  if (error || !proposal) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button variant="ghost" onClick={() => router.back()}>
            <Icon name="ArrowLeft" size="sm" className="mr-2" />
            Back
          </Button>
        </div>
        <Card>
          <CardContent className="py-12 text-center">
            <Icon name="AlertCircle" size="xl" className="mx-auto mb-4 text-muted-foreground" />
            <h3 className="text-lg font-semibold mb-2">
              {error || 'Proposal not found'}
            </h3>
            <p className="text-muted-foreground mb-4">
              The proposal you&#39;re looking for doesn&#39;t exist or you don&#39;t have permission to view it.
            </p>
            <Button asChild>
              <Link href="/freelancer/proposals">
                View All Proposals
              </Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" onClick={() => router.back()}>
            <Icon name="ArrowLeft" size="sm" className="mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-2xl font-bold">Proposal Details</h1>
            <p className="text-muted-foreground">
              Submitted {formatDistanceToNow(new Date(proposal.createdAt), { addSuffix: true })}
            </p>
          </div>
        </div>
        {getStatusBadge(proposal.status)}
      </div>

      {/* Job Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Icon name="Briefcase" size="md" />
            Job Information
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h3 className="font-semibold text-lg mb-2">
              {proposal.job?.title || 'Job Title Not Available'}
            </h3>
            <p className="text-muted-foreground">
              {proposal.job?.description || 'No description available'}
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 pt-4 border-t">
            <div>
              <label className="text-sm font-medium text-muted-foreground">Budget</label>
              <p className="text-lg font-semibold">
                ${proposal.job?.budget?.toLocaleString() || 'N/A'}
              </p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">Client</label>
              <p className="font-medium">
                {proposal.job?.client?.name || 'Client information not available'}
              </p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">Work Type</label>
              <p className="font-medium">
                {proposal.job?.isRemote ? 'Remote' : 'On-site'}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Proposal Details */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Icon name="FileText" size="md" />
            Your Proposal
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium text-muted-foreground">Bid Amount</label>
              <p className="text-2xl font-bold text-primary">
                ${proposal.bidAmount?.toLocaleString() || '0'}
              </p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">Proposed Rate</label>
              <p className="text-2xl font-bold">
                ${proposal.proposedRate?.toLocaleString() || '0'}/hr
              </p>
            </div>
          </div>
          
          <div className="pt-4 border-t">
            <label className="text-sm font-medium text-muted-foreground">Cover Letter</label>
            <div className="mt-2 p-4 bg-muted/50 rounded-lg">
              <p className="whitespace-pre-wrap">
                {proposal.coverLetter || 'No cover letter provided'}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Actions */}
      <Card>
        <CardContent className="py-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <Button asChild className="flex-1">
              <Link href={`/freelancer/jobs/${proposal.jobId}`}>
                <Icon name="Eye" size="sm" className="mr-2" />
                View Job Details
              </Link>
            </Button>
            
            {proposal.status === 'PENDING' && (
              <Button variant="outline" className="flex-1">
                <Icon name="Edit" size="sm" className="mr-2" />
                Edit Proposal
              </Button>
            )}
            
            <Button variant="outline" asChild className="flex-1">
              <Link href="/freelancer/proposals">
                <Icon name="List" size="sm" className="mr-2" />
                All Proposals
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ProposalDetailsPage;
