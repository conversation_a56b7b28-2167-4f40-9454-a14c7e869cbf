import { graphQLClient } from '@/lib/graphql/graphqlClient';
import { GET_JOB, LIST_JOBS, LIST_MY_JOBS } from '../graphql/queries';
import {
  CREATE_JOB,
  UPDATE_JOB,
  DELETE_JOB,
  SUBMIT_PROPOSAL,
  UPDATE_PROPOSAL_STATUS,
  WITHDRAW_PROPOSAL,
} from '../graphql/mutations';
import { 
  Job, 
  CreateJobInput, 
  UpdateJobInput, 
  JobFilter, 
  JobProposal, 
  JobWithProposals 
} from '@/types/job';
import { CreateJobProposalInput } from '@/types/proposal';

function handleApiError(operation: string, error: unknown): never {
  const errorMessage = error instanceof Error ? error.message : 'Unknown error';
  console.error(`Error in ${operation}:`, error);
  throw new Error(`Failed to ${operation.toLowerCase()}: ${errorMessage}`);
}

export class JobService {
  static async getJob(id: string): Promise<Job> {
    try {
      const response = await graphQLClient.query(
        GET_JOB,
        { id }
      ) as { getJob: Job };
      return response.getJob;
    } catch (error) {
      return handleApiError('getJob', error);
    }
  }

  static async listJobs(filter?: JobFilter): Promise<{ items: Job[]; nextToken?: string }> {
    try {
      const response = await graphQLClient.query(
        LIST_JOBS,
        { filter }
      ) as { listJobs: { items: Job[]; nextToken?: string } };
      return response.listJobs;
    } catch (error) {
      return handleApiError('listJobs', error);
    }
  }

  static async listMyJobs(clientId: string, status?: string): Promise<Job[]> {
    try {
      const response = await graphQLClient.query(
        LIST_MY_JOBS,
        { clientId, status }
      ) as { listMyJobs: { items: Job[] } };
      return response.listMyJobs.items;
    } catch (error) {
      return handleApiError('listMyJobs', error);
    }
  }

  static async createJob(input: CreateJobInput): Promise<Job> {
    try {
      const response = await graphQLClient.mutate<{ createJob: Job }>(
        CREATE_JOB,
        { input },
        { authMode: 'userPool' }
      ) as { createJob: Job };
      
      return response.createJob;
    } catch (error) {
      console.error('Error in createJob:', error);
      return handleApiError('createJob', error);
    }
  }

  static async updateJob(input: UpdateJobInput): Promise<Job> {
    try {
      const response = await graphQLClient.mutate(
        UPDATE_JOB,
        { input }
      ) as { updateJob: Job };
      return response.updateJob;
    } catch (error) {
      return handleApiError('updateJob', error);
    }
  }

  static async deleteJob(id: string): Promise<Job> {
    try {
      const response = await graphQLClient.mutate(
        DELETE_JOB,
        { input: { id } },
        { authMode: 'userPool' }
      ) as { deleteJob: Job };
      return response.deleteJob;
    } catch (error) {
      return handleApiError('deleteJob', error);
    }
  }

  static async submitProposal(input: CreateJobProposalInput): Promise<JobProposal> {
    try {
      const response = await graphQLClient.mutate(
        SUBMIT_PROPOSAL,
        { input }
      ) as { submitProposal: JobProposal };
      return response.submitProposal;
    } catch (error) {
      return handleApiError('submitProposal', error);
    }
  }

  static async updateProposalStatus(id: string, status: 'PENDING' | 'ACCEPTED' | 'REJECTED'): Promise<JobProposal> {
    try {
      const response = await graphQLClient.mutate(
        UPDATE_PROPOSAL_STATUS,
        { 
          input: { 
            id, 
            status 
          } 
        }
      ) as { updateProposal: JobProposal };
      return response.updateProposal;
    } catch (error) {
      return handleApiError('updateProposalStatus', error);
    }
  }

  static async withdrawProposal(id: string): Promise<JobProposal> {
    try {
      const response = await graphQLClient.mutate(
        WITHDRAW_PROPOSAL,
        { id }
      ) as { withdrawProposal: JobProposal };
      return response.withdrawProposal;
    } catch (error) {
      return handleApiError('withdrawProposal', error);
    }
  }

  static async getJobProposals(jobId: string): Promise<JobProposal[]> {
    try {
      const job = await this.getJob(jobId) as JobWithProposals;
      
      if (job && 'proposals' in job && Array.isArray(job.proposals)) {
        return job.proposals;
      }
      
      return [];
    } catch (error) {
      return handleApiError('getJobProposals', error);
    }
  }

  static async listMyProposals(): Promise<JobProposal[]> {
    // TODO: Implement job proposals listing
    return [];
  }
}

export const jobService = new JobService();
export default jobService;
