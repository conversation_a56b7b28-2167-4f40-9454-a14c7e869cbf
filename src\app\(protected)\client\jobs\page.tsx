'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useAuth } from '@/lib/auth/AuthContext';
import { jobService } from '@/api/jobService';
import Link from 'next/link';
import { Button, ConfirmDialog } from '@/components/ui';
import { Card, CardContent } from '@/components/ui/Card';
import { Table, Column } from '@/components/ui/Table';
import { Icon } from '@/components/ui';
import { ContentHeader } from '@/components/layout/ContentHeader';
import { formatDistanceToNow } from 'date-fns';

type JobWithApplicationCount = {
  id: string | number;
  title: string;
  description: string;
  category: string;
  budget: number;
  deadline: string;
  clientId: string;
  client?: {
    id: string;
    name: string;
    email: string;
    profilePhoto?: string;
  };
  status: string;
  isRemote: boolean;
  location: string;
  createdAt: string;
  updatedAt: string;
  applicationCount: number;
  skills: string;
  _proposals: string;
  hasAcceptedProposal?: boolean;
  [key: string]: string | number | boolean | Record<string, unknown> | null | undefined;
};

const ITEMS_PER_PAGE = 5;

const ClientJobsPage = () => {
  const { isAuthenticated, user, loading: authLoading } = useAuth();
  const router = useRouter();
  const searchParams = useSearchParams();
  const [jobs, setJobs] = useState<JobWithApplicationCount[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [deleteLoading, setDeleteLoading] = useState<string | null>(null);
  const [totalItems, setTotalItems] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [jobToDelete, setJobToDelete] = useState<string | null>(null);
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState({
    status: '',
    category: '',
    sortBy: 'newest'
  });

  useEffect(() => {
    if (!authLoading && (!isAuthenticated || user?.attributes?.['custom:role'] !== 'CLIENT')) {
      router.push('/login');
    }
  }, [authLoading, isAuthenticated, user, router]);

  const fetchJobs = useCallback(async () => {
    if (!isAuthenticated || !user?.username) {
      return;
    }
    
    try {
      setIsLoading(true);
      const page = searchParams.get('page') ? parseInt(searchParams.get('page') as string) : 1;
      
      // Fetch jobs with their proposals and status
      let allJobs = await jobService.listMyJobs(user.username);
      
      let filteredJobs = [...allJobs];
      
      if (filters.status) {
        filteredJobs = filteredJobs.filter(job => job.status === filters.status);
      }
      
      if (filters.category) {
        filteredJobs = filteredJobs.filter(job => job.category === filters.category);
      }
      
      // Sort the filtered jobs
      const sortedJobs = [...filteredJobs].sort((a, b) => {
        switch (filters.sortBy) {
          case 'oldest':
            return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
          case 'budget_high':
            return (b.budget || 0) - (a.budget || 0);
          case 'budget_low':
            return (a.budget || 0) - (b.budget || 0);
          case 'newest':
          default:
            return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
        }
      });
      
      const totalItems = sortedJobs.length;
      const totalPages = Math.ceil(totalItems / ITEMS_PER_PAGE) || 1;
      
      const validPage = Math.min(Math.max(1, page), totalPages);
      
      const startIndex = (validPage - 1) * ITEMS_PER_PAGE;
      const endIndex = Math.min(startIndex + ITEMS_PER_PAGE, totalItems);
      const paginatedJobs = sortedJobs.slice(startIndex, endIndex);
      
      setJobs(paginatedJobs);
      setTotalItems(totalItems);
      setCurrentPage(validPage);
      
      if (page !== validPage) {
        const params = new URLSearchParams(searchParams);
        params.set('page', validPage.toString());
        router.replace(`?${params.toString()}`, { scroll: false });
      }
      
    } catch (error) {
      console.error('Error fetching jobs:', error);
      setJobs([]);
      setTotalItems(0);
      setCurrentPage(1);
      if (searchParams.get('page') !== '1') {
        router.replace('?page=1', { scroll: false });
      }
    } finally {
      setIsLoading(false);
    }
  }, [isAuthenticated, user?.username, searchParams, router, filters]);

  useEffect(() => {
    if (isAuthenticated && user?.attributes?.['custom:role'] === 'CLIENT') {
      fetchJobs();
    }
  }, [isAuthenticated, user, fetchJobs]);

  const handleConfirmDelete = useCallback(async () => {
    if (!jobToDelete) return;

    try {
      setDeleteLoading(jobToDelete);
      await jobService.deleteJob(jobToDelete);
      await fetchJobs();
    } catch (error) {
      console.error('Error deleting job:', error);
    } finally {
      setDeleteLoading(null);
      setJobToDelete(null);
      setShowDeleteDialog(false);
    }
  }, [jobToDelete, fetchJobs]);

  const handleCancelDelete = () => {
    setShowDeleteDialog(false);
    setJobToDelete(null);
  };

  const handlePageChange = (page: number) => {
    if (page !== currentPage) {
      const params = new URLSearchParams(searchParams);
      params.set('page', page.toString());
      router.replace(`?${params.toString()}`, { scroll: false });
    }
  };

  const columns: Column<JobWithApplicationCount>[] = [
    {
      header: 'Job Title',
      accessor: 'title',
      sortable: true,
      cell: (value: unknown, row: JobWithApplicationCount) => {
        const title = value as string;
        return (
          <div className="flex flex-col">
            <span className="font-medium text-foreground">{title}</span>
            {row.client && (
              <span className="text-xs text-muted-foreground">
                Posted by: {row.client.name || row.client.email}
              </span>
            )}
          </div>
        );
      },
    },
    {
      header: 'Status',
      accessor: 'status',
      sortable: true,
      cell: (value: unknown) => {
        const status = value as string;
        const statusConfig = {
          OPEN: { label: 'Open', color: 'bg-green-100 text-green-800' },
          IN_PROGRESS: { label: 'In Progress', color: 'bg-blue-100 text-blue-800' },
          COMPLETED: { label: 'Completed', color: 'bg-purple-100 text-purple-800' },
          CANCELLED: { label: 'Cancelled', color: 'bg-gray-100 text-gray-800' },
        } as const;
        
        type StatusKey = keyof typeof statusConfig;
        const statusKey = status as StatusKey;
        const config = statusKey in statusConfig 
          ? statusConfig[statusKey] 
          : { label: status, color: 'bg-gray-100 text-gray-800' };
        
        return (
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
            {config.label}
          </span>
        );
      },
    },
    {
      header: 'Posted',
      accessor: 'createdAt',
      sortable: true,
      cell: (value: unknown) => {
        if (!value) return <span className="text-sm text-muted-foreground">N/A</span>;
        const dateValue = value as string | Date;
        const date = typeof dateValue === 'string' ? new Date(dateValue) : dateValue;
        return (
          <span className="text-sm text-muted-foreground">
            {formatDistanceToNow(date, { addSuffix: true })}
          </span>
        );
      },
    },
    {
      header: 'Proposals',
      accessor: (row: JobWithApplicationCount) => row.applicationCount || 0,
      cell: (value: unknown) => {
        const count = value as number;
        return (
          <div className="text-center">
            {count > 0 ? (
              <span className="px-2 py-1 bg-primary/10 text-primary text-xs font-medium rounded-full">
                {count} {count === 1 ? 'proposal' : 'proposals'}
              </span>
            ) : (
              <span className="text-muted-foreground">No proposals</span>
            )}
          </div>
        );
      },
    },
    {
      header: 'Budget',
      accessor: 'budget',
      sortable: true,
      cell: (value: unknown) => {
        const budget = value as number | null | undefined;
        return <span className="font-medium">${budget?.toLocaleString() || '-'}</span>;
      },
    },
    {
      header: 'Actions',
      accessor: 'id',
      cell: (_: unknown, row: JobWithApplicationCount) => {
        const handleViewClick = (e: React.MouseEvent) => {
          e.stopPropagation();
          router.push(`/jobs/${row.id}`);
        };
        
        const handleEditClick = (e: React.MouseEvent) => {
          e.stopPropagation();
          router.push(`/client/jobs/${row.id}/edit`);
        };
        
        const onDeleteClick = (e: React.MouseEvent) => {
          e.stopPropagation();
          setJobToDelete(row.id.toString());
          setShowDeleteDialog(true);
        };
        
        return (
          <div className="flex items-center justify-start space-x-1 w-full">
            <Button
              variant="ghost"
              size="icon"
              onClick={handleViewClick}
              className="h-8 w-8 text-muted-foreground hover:text-foreground flex items-center justify-center"
              title="View Job"
            >
              <Icon name="Eye" size='sm' />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={handleEditClick}
              className="h-8 w-8 text-muted-foreground hover:text-foreground flex items-center justify-center"
              title="Edit Job"
            >
              <Icon name="Edit" size='sm' />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={onDeleteClick}
              disabled={deleteLoading === row.id}
              className="h-8 w-8 text-muted-foreground hover:text-destructive hover:bg-destructive/10"
              title="Delete Job"
            >
              {deleteLoading === row.id ? (
                <Icon name="Loader2" size='sm' className="animate-spin" />
              ) : (
                <Icon name="Trash2" size='sm' />
              )}
            </Button>
          </div>
        );
      },
      className: 'text-right',
    },
  ];

  const tablePagination = {
    enabled: true,
    currentPage,
    pageSize: ITEMS_PER_PAGE,
    totalItems,
    totalPages: Math.ceil(totalItems / ITEMS_PER_PAGE),
    onPageChange: handlePageChange,
    showFirstLast: true,
    showPrevNext: true,
    className: 'mt-4',
  };

  if (authLoading || !isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Icon name="Loader2" size="xl" className="animate-spin text-primary" />
      </div>
    );
  }

  return (
    <div className="w-full max-w-7xl mx-auto p-4 sm:p-6 space-y-6">
      <div className="flex justify-between items-center mb-6">
        <ContentHeader
          title="My Job Postings"
          subtitle="Manage your job postings and proposals"
          breadcrumbs={[
            { label: 'Dashboard', href: '/client/dashboard' },
            { label: 'Jobs', current: true }
          ]}
        />
        <div className="flex items-center space-x-3">
          <Button
            variant="outline"
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center space-x-2"
          >
            <Icon name={showFilters ? 'FilterX' : 'Filter'} size="sm" />
            <span>{showFilters ? 'Hide Filters' : 'Filters'}</span>
          </Button>
          <Button asChild>
            <Link href="/client/jobs/new">
              <Icon name="Plus" size="sm" className="mr-2" />
              Post a Job
            </Link>
          </Button>
        </div>
      </div>

      {showFilters && (
        <Card className="mb-6">
          <CardContent className="p-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-muted-foreground mb-1">Status</label>
                <select
                  className="w-full p-2 border rounded-md"
                  value={filters.status}
                  onChange={(e) => setFilters({...filters, status: e.target.value})}
                >
                  <option value="">All Statuses</option>
                  <option value="OPEN">Open</option>
                  <option value="IN_PROGRESS">In Progress</option>
                  <option value="COMPLETED">Completed</option>
                  <option value="CANCELLED">Cancelled</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-muted-foreground mb-1">Category</label>
                <select
                  className="w-full p-2 border rounded-md"
                  value={filters.category}
                  onChange={(e) => setFilters({...filters, category: e.target.value})}
                >
                  <option value="">All Categories</option>
                  <option value="WEB_DEVELOPMENT">Web Development</option>
                  <option value="MOBILE_DEVELOPMENT">Mobile Development</option>
                  <option value="DESIGN">Design</option>
                  <option value="WRITING">Writing</option>
                  <option value="MARKETING">Marketing</option>
                  <option value="BUSINESS">Business</option>
                  <option value="ACCOUNTING">Accounting</option>
                  <option value="LEGAL">Legal</option>
                  <option value="OTHER">Other</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-muted-foreground mb-1">Sort By</label>
                <select
                  className="w-full p-2 border rounded-md"
                  value={filters.sortBy}
                  onChange={(e) => setFilters({...filters, sortBy: e.target.value})}
                >
                  <option value="newest">Newest First</option>
                  <option value="oldest">Oldest First</option>
                  <option value="budget_high">Budget: High to Low</option>
                  <option value="budget_low">Budget: Low to High</option>
                </select>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      <Card>
        <CardContent className="p-0">
          {isLoading ? (
            <div className="flex items-center justify-center p-12">
              <Icon name="Loader2" size="xl" className="animate-spin text-primary" />
            </div>
          ) : jobs.length > 0 ? (
            <Table
              data={jobs}
              columns={columns}
              keyField="id"
              pagination={tablePagination}
              className="w-full"
              rowClassName="hover:bg-muted/50 cursor-pointer"
              onRowClick={(row) => router.push(`/client/jobs/${row.id}`)}
            />
          ) : (
            <div className="text-center p-12">
              <div className="mx-auto w-16 h-16 rounded-full bg-muted flex items-center justify-center mb-4">
                <Icon name="Briefcase" size="xl" className="text-muted-foreground" />
              </div>
              <h3 className="text-lg font-medium text-foreground mb-1">No jobs found</h3>
              <p className="text-muted-foreground mb-6">
                {Object.values(filters).some(Boolean) 
                  ? 'Try adjusting your filters or create a new job.'
                  : 'Get started by creating a new job.'}
              </p>
              <Button asChild>
                <Link href="/client/jobs/new">
                  <Icon name="Plus" size="sm" className="mr-2" />
                  Post a Job
                </Link>
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      <ConfirmDialog
        isOpen={showDeleteDialog}
        onClose={handleCancelDelete}
        onConfirm={handleConfirmDelete}
        title="Delete Job"
        description="Are you sure you want to delete this job? This action cannot be undone."
        confirmText={deleteLoading ? 'Deleting...' : 'Delete'}
        cancelText="Cancel"
        variant="destructive"
        isLoading={!!deleteLoading}
      />
    </div>
  );
};

export default ClientJobsPage;
