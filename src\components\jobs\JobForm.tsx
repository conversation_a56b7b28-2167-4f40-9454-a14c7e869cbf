import React from 'react';
import { use<PERSON><PERSON>, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { CreateJobInput, JobCategory } from '@/types/job';
import { Form, FormField, Input, Textarea, Select, Button, Loading, Card, CardContent, CardHeader, CardTitle } from '@/components/ui';
import { DatePicker } from '@/components/ui/DatePicker';

const jobCategories: { value: JobCategory; label: string }[] = [
  { value: 'WEB_DEVELOPMENT', label: 'Web Development' },
  { value: 'MOBILE_DEVELOPMENT', label: 'Mobile Development' },
  { value: 'DESIGN', label: 'Design' },
  { value: 'WRITING', label: 'Writing' },
  { value: 'MARKETING', label: 'Marketing' },
  { value: 'BUSINESS', label: 'Business' },
  { value: 'OTHER', label: 'Other' },
];

type JobFormData = {
  title: string;
  description: string;
  budget: number;
  category: JobCategory;
  deadline: Date | null;
};

// Define the form data type
// JobFormData type is defined above with the same structure

const jobSchema = yup.object().shape({
  title: yup.string().required('Title is required').max(100, 'Title is too long'),
  description: yup.string().required('Description is required').max(5000, 'Description is too long'),
  budget: yup
    .number()
    .typeError('Budget must be a number')
    .required('Budget is required')
    .min(5, 'Minimum budget is $5')
    .max(1000000, 'Maximum budget is $1,000,000'),
  category: yup
    .string()
    .oneOf([...jobCategories.map(cat => cat.value)] as const)
    .required('Category is required'),
  deadline: yup
    .date()
    .typeError('Please select a valid date')
    .required('Deadline is required')
    .min(new Date(), 'Deadline must be in the future')
    .nullable()
});

interface JobFormProps {
  initialData?: Partial<JobFormData>;
  onSubmit: (data: CreateJobInput) => Promise<void>;
  isSubmitting: boolean;
  submitButtonText?: string;
  submitButtonClassName?: string;
}

export const JobForm: React.FC<JobFormProps> = ({
  initialData,
  onSubmit,
  isSubmitting,
  submitButtonText = 'Post Job',
  submitButtonClassName = '',
}: JobFormProps) => {
  const form = useForm<JobFormData>({
    resolver: yupResolver(jobSchema),
    defaultValues: {
      title: initialData?.title || '',
      description: initialData?.description || '',
      budget: initialData?.budget || 0,
      category: initialData?.category || jobCategories[0]?.value || '',
      deadline: initialData?.deadline ? new Date(initialData.deadline) : null,
    },
  });

  const { register, handleSubmit, control, formState: { errors } } = form;

  const onSubmitForm = async (formData: JobFormData) => {
    if (!formData.deadline) {
      console.error('Deadline is required');
      return;
    }

    const jobData: CreateJobInput = {
      title: formData.title,
      description: formData.description,
      budget: formData.budget,
      category: formData.category,
      deadline: formData.deadline.toISOString()
    };
    
    try {
      await onSubmit(jobData);
    } catch (error) {
      console.error('Error submitting job:', error);
      // You might want to show an error toast/message to the user here
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Job Details</CardTitle>
      </CardHeader>
      <CardContent>
        <Form onSubmit={handleSubmit(onSubmitForm)}>
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
            <FormField
              label="Job Title"
              required
              error={errors.title?.message}
            >
              <Input
                {...register('title')}
                placeholder="e.g. Website Development"
                error={!!errors.title}
              />
            </FormField>

            <FormField
              label="Category"
              required
              error={errors.category?.message}
            >
              <Select
                {...register('category')}
                options={[
                  { value: '', label: 'Select a category' },
                  ...jobCategories.map(cat => ({ value: cat.value, label: cat.label }))
                ]}
                placeholder="Select a category"
                error={!!errors.category}
              />
            </FormField>
          </div>

          <FormField
            label="Job Description"
            required
            error={errors.description?.message}
            hint="Provide a detailed description of the job requirements and expectations"
          >
            <Textarea
              {...register('description')}
              rows={6}
              placeholder="Describe the job in detail..."
              error={!!errors.description}
            />
          </FormField>

          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
            <FormField
              label="Budget"
              required
              error={errors.budget?.message}
              hint="Enter the total budget for this project in USD"
            >
              <Input
                type="number"
                {...register('budget')}
                placeholder="0.00"
                step="0.01"
                min="0"
                prefix="$"
                error={!!errors.budget}
                className="w-full"
              />
            </FormField>

            <FormField
              label="Deadline"
              required
              error={errors.deadline?.message}
              hint="When do you need this project completed?"
            >
              <Controller
                name="deadline"
                control={control}
                render={({ field }) => (
                  <DatePicker
                    value={field.value}
                    onChange={(date) => field.onChange(date || null)}
                    placeholder="Select deadline"
                    minDate={new Date()}
                    error={!!errors.deadline}
                  />
                )}
              />
            </FormField>
          </div>

          <div className="flex justify-end pt-4">
            <Button
              type="submit"
              disabled={isSubmitting}
              className={`min-w-[120px] ${submitButtonClassName}`}
            >
              {isSubmitting ? (
                <>
                  <Loading size="sm" className="mr-2" />
                  Submitting...
                </>
              ) : (
                submitButtonText
              )}
            </Button>
          </div>
        </Form>
      </CardContent>
    </Card>
  );
};

export default JobForm;
