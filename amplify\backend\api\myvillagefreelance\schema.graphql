
# Freelance Marketplace Amplify Schema

enum UserRole {
  CLIENT
  FREELANCER
}

enum JobStatus {
  OPEN
  IN_PROGRESS
  COMPLETED
  CANCELLED
}

type User @model @auth(rules: [{ allow: private }]) {
  id: ID!
  name: String!
  email: AWSEmail!
  role: UserRole!
  cognitoId: ID! @index(name: "byCognitoId")
  profilePhoto: String
  bio: String
  skills: [String]

  jobs: [Job] @hasMany(fields: ["id"])
  proposals: [Proposal] @hasMany(fields: ["id"])
  conversations: [Conversation] @hasMany(fields: ["id"])
  messages: [Message] @hasMany(fields: ["id"])
}

type Job @model @auth(rules: [{ allow: private }]) {
  id: ID!
  title: String!
  description: String!
  category: String!
  budget: Float!
  deadline: AWSDateTime
  isRemote: Boolean
  skills: [String]
  status: JobStatus @default(value: "OPEN")
  createdAt: AWSDateTime
  updatedAt: AWSDateTime

  clientId: ID!
  client: User @belongsTo(fields: ["clientId"])
  proposalId: ID
  proposals: [Proposal] @hasMany(indexName: "byJob", fields: ["proposalId"])
}

enum ProposalStatus {
  PENDING
  ACCEPTED
  REJECTED
}

type Proposal @model @auth(rules: [{ allow: private }]) {
  id: ID!
  bidAmount: Float!
  coverLetter: String
  status: ProposalStatus!
  proposedRate: Float
  createdAt: AWSDateTime
  updatedAt: AWSDateTime

  freelancerId: ID!
  freelancer: User @belongsTo(fields: ["freelancerId"])
  jobId: ID! @index(name: "byJob")
  job: Job @belongsTo(fields: ["jobId"])
}

type Conversation @model @auth(rules: [{ allow: private }]) {
  id: ID!
  createdAt: AWSDateTime!
  updatedAt: AWSDateTime!

  jobId: ID! # reference to Job
  clientId: ID! # reference to User
  freelancerId: ID! # reference to User

  messages: [Message] @hasMany(fields: ["id"])
}

type Message @model @auth(rules: [{ allow: private }]) {
  id: ID!
  messageText: String!
  createdAt: AWSDateTime!
  updatedAt: AWSDateTime!

  conversationId: ID!
  conversation: Conversation @belongsTo(fields: ["conversationId"])
  senderId: ID!
  sender: User @belongsTo(fields: ["senderId"])
  receiverId: ID!
  receiver: User @belongsTo(fields: ["receiverId"])
}

enum ContractStatus {
  IN_PROGRESS
  COMPLETED
}

type Contract @model @auth(rules: [{ allow: private }]) {
  id: ID!
  status: ContractStatus!
  createdAt: AWSDateTime!
  updatedAt: AWSDateTime!

  jobId: ID! # reference to Job
  clientId: ID! # reference to User
  freelancerId: ID! # reference to User
}

enum PaymentStatus {
  PAID
  PENDING
}

enum PaymentMethod {
  STRIPE
  USDC
  MYVILLAGETOKEN
}

type Payment @model @auth(rules: [{ allow: private }]) {
  id: ID!
  amount: Float!
  status: PaymentStatus!
  method: PaymentMethod!
  createdAt: AWSDateTime!
  updatedAt: AWSDateTime!

  contractId: ID! # reference to Contract
}
