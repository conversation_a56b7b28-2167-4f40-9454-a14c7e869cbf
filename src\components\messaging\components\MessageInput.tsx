'use client';

import { useState, useRef, useCallback } from 'react';
import { Button } from '@/components/ui/Button';
import { cn } from '@/lib/utils';
import { Send, Paperclip, Smile, X } from 'lucide-react';

export interface MessageInputProps {
  onSend: (content: string) => void;
  onTyping?: (isTyping: boolean) => void;
  onFileUpload?: (file: File) => void;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
  maxLength?: number;
  allowFiles?: boolean;
  allowEmojis?: boolean;
}

export function MessageInput({
  onSend,
  onTyping,
  onFileUpload,
  placeholder = 'Type your message...',
  disabled = false,
  className,
  maxLength = 1000,
  allowFiles = true,
  allowEmojis = false,
}: MessageInputProps) {
  const [message, setMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const handleTypingChange = useCallback((typing: boolean) => {
    if (typing !== isTyping) {
      setIsTyping(typing);
      onTyping?.(typing);
    }
  }, [isTyping, onTyping]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const trimmedMessage = message.trim();
    if (!trimmedMessage && !selectedFile) return;
    
    if (selectedFile) {
      onFileUpload?.(selectedFile);
      setSelectedFile(null);
    }
    
    if (trimmedMessage) {
      onSend(trimmedMessage);
      setMessage('');
    }
    
    handleTypingChange(false);
    
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const { value } = e.target;
    
    if (value.length <= maxLength) {
      setMessage(value);
      
      if (textareaRef.current) {
        textareaRef.current.style.height = 'auto';
        textareaRef.current.style.height = `${Math.min(textareaRef.current.scrollHeight, 120)}px`;
      }
      
      handleTypingChange(value.length > 0);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  const handleFileSelect = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setSelectedFile(file);
    }
    if (e.target) {
      e.target.value = '';
    }
  };

  const removeSelectedFile = () => {
    setSelectedFile(null);
  };

  const handleEmojiClick = () => {
    console.log('Emoji picker clicked');
  };

  const isSubmitDisabled = disabled || (!message.trim() && !selectedFile);

  return (
    <div className={cn('border-t border-border bg-background', className)}>
      {/* Selected File Preview */}
      {selectedFile && (
        <div className="p-3 border-b border-border bg-muted/50">
          <div className="flex items-center gap-2 text-sm">
            <Paperclip className="w-4 h-4 text-muted-foreground" />
            <span className="flex-1 truncate">{selectedFile.name}</span>
            <span className="text-muted-foreground">
              ({(selectedFile.size / 1024 / 1024).toFixed(2)} MB)
            </span>
            <Button
              variant="ghost"
              size="sm"
              onClick={removeSelectedFile}
              className="h-6 w-6 p-0"
            >
              <X className="w-4 h-4" />
            </Button>
          </div>
        </div>
      )}

      {/* Input Form */}
      <form onSubmit={handleSubmit} className="p-4">
        <div className="flex items-end gap-2">
          {/* File Upload Button */}
          {allowFiles && (
            <Button
              type="button"
              variant="ghost"
              size="icon"
              onClick={handleFileSelect}
              disabled={disabled}
              className="flex-shrink-0 h-10 w-10"
              title="Attach file"
            >
              <Paperclip className="w-5 h-5" />
              <input
                type="file"
                ref={fileInputRef}
                onChange={handleFileChange}
                className="hidden"
                disabled={disabled}
                accept="image/*,.pdf,.doc,.docx,.xls,.xlsx,.txt"
              />
            </Button>
          )}

          {/* Message Input */}
          <div className="flex-1 relative">
            <textarea
              ref={textareaRef}
              value={message}
              onChange={handleChange}
              onKeyDown={handleKeyDown}
              placeholder={placeholder}
              disabled={disabled}
              className={cn(
                'w-full min-h-[40px] max-h-[120px] px-4 py-2 pr-12 bg-background border border-input rounded-lg',
                'focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent',
                'resize-none text-sm placeholder:text-muted-foreground',
                'disabled:opacity-50 disabled:cursor-not-allowed'
              )}
              rows={1}
              maxLength={maxLength}
            />
            
            {/* Character Counter */}
            {message.length > maxLength * 0.8 && (
              <div className="absolute bottom-1 right-1 text-xs text-muted-foreground bg-background px-1 rounded">
                {message.length}/{maxLength}
              </div>
            )}
          </div>

          {/* Emoji Button */}
          {allowEmojis && (
            <Button
              type="button"
              variant="ghost"
              size="icon"
              onClick={handleEmojiClick}
              disabled={disabled}
              className="flex-shrink-0 h-10 w-10"
              title="Add emoji"
            >
              <Smile className="w-5 h-5" />
            </Button>
          )}

          {/* Send Button */}
          <Button
            type="submit"
            size="icon"
            disabled={isSubmitDisabled}
            className="flex-shrink-0 h-10 w-10"
            title="Send message"
          >
            <Send className="w-4 h-4" />
          </Button>
        </div>

        {/* Helper Text */}
        <div className="mt-2 flex items-center justify-between text-xs text-muted-foreground">
          <span>Press Enter to send, Shift+Enter for new line</span>
          {isTyping && (
            <span className="text-primary">Typing...</span>
          )}
        </div>
      </form>
    </div>
  );
}
