'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { useAuth } from '@/lib/auth/AuthContext';
import { Job, UpdateJobInput } from '@/types/job';
import { jobService } from '@/api/jobService';
import JobForm from '@/components/jobs/JobForm';
import { Icon } from '@/components/ui';
import { ContentHeader } from '@/components/layout/ContentHeader';

export default function EditJobPage() {
  const { id } = useParams<{ id: string }>();
  const { isAuthenticated, user, loading: authLoading } = useAuth();
  const router = useRouter();
  const [job, setJob] = useState<Job | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!id || !isAuthenticated) return;

    const fetchJob = async () => {
      try {
        setIsLoading(true);
        const jobData = await jobService.getJob(id);
        
        if (jobData.clientId !== user?.username) {
          router.push('/client/jobs');
          return;
        }
        
        setJob(jobData);
      } catch (err) {
        console.error('Error fetching job:', err);
        setError('Failed to load job data.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchJob();
  }, [id, isAuthenticated, user, router]);

  const handleSubmit = async (data: Omit<UpdateJobInput, 'id'>) => {
    try {
      setIsSubmitting(true);
      setError(null);
      
      await jobService.updateJob({
        ...data,
        id: id,
      });
      
      router.push('/client/jobs');
    } catch (err) {
      console.error('Error updating job:', err);
      setError('Failed to update job. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!authLoading && (!isAuthenticated || user?.attributes?.['custom:role'] !== 'CLIENT')) {
    router.push('/login');
    return null;
  }

  if (authLoading || !isAuthenticated || isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <Icon name="Loader2" size="xl" className="animate-spin text-blue-500" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-full px-4">
        <div className="max-w-md w-full bg-card p-6 rounded-lg shadow-sm border text-center">
          <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
            <Icon name="XCircle" size="lg" className="text-red-600" />
          </div>
          <h3 className="mt-3 text-lg font-medium text-foreground">Error</h3>
          <p className="mt-2 text-sm text-muted-foreground">{error}</p>
          <div className="mt-6">
            <button
              type="button"
              onClick={() => router.push('/client/jobs')}
              className="inline-flex items-center rounded-md bg-primary px-4 py-2 text-sm font-medium text-primary-foreground shadow-sm hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
            >
              Back
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (!job) {
    return (
      <div className="flex items-center justify-center h-full px-4">
        <div className="max-w-md w-full bg-card p-6 rounded-lg shadow-sm border text-center">
          <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-blue-100">
            <Icon name="Info" size="lg" className="text-blue-600" />
          </div>
          <h3 className="mt-3 text-lg font-medium text-foreground">Job Not Found</h3>
          <p className="mt-2 text-sm text-muted-foreground">The job you&#39;re looking for doesn&#39;t exist or you don&#39;t have permission to view it.</p>
          <div className="mt-6">
            <button
              type="button"
              onClick={() => router.push('/client/jobs')}
              className="inline-flex items-center rounded-md bg-primary px-4 py-2 text-sm font-medium text-primary-foreground shadow-sm hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
            >
              Back
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full max-w-6xl mx-auto p-4 sm:p-6">
      <ContentHeader
        title="Edit Job"
        subtitle="Update the details of your job posting."
        breadcrumbs={[
          { label: 'Dashboard', href: '/client/dashboard' },
          { label: 'Jobs', href: '/client/jobs' },
          { label: job?.title || 'Job', href: `/client/jobs/${id}` },
          { label: 'Edit', current: true }
        ]}
        showBackButton={true}
        backButtonLabel="Back"
      />
      {error && (
        <div className="rounded-md bg-red-50 p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <Icon name="XCircle" size="sm" className="text-red-400" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-red-800">{error}</p>
            </div>
          </div>
        </div>
      )}
      
      <div className="bg-card rounded-lg shadow-sm p-0">
        <JobForm 
          initialData={job ? {
            ...job,
            deadline: job.deadline ? new Date(job.deadline) : null
          } : undefined}
          onSubmit={handleSubmit}
          isSubmitting={isSubmitting}
          submitButtonText="Update Job"
          submitButtonClassName="cursor-pointer"
        />
      </div>
    </div>
  );
}
