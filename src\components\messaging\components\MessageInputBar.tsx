'use client';

import { useState, useRef } from 'react';
import { Button } from '@/components/ui/Button';
import { Paperclip, Send, Smile } from 'lucide-react';

export interface MessageInputBarProps {
  onSend: (content: string) => void;
  onTyping: (isTyping: boolean) => void;
  placeholder?: string;
  disabled?: boolean;
  onFileUpload?: (file: File) => void;
}

export function MessageInputBar({
  onSend,
  onTyping,
  placeholder = 'Type a message...',
  disabled = false,
  onFileUpload,
}: MessageInputBarProps) {
  const [message, setMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  // const typingTimeout = useRef<NodeJS.Timeout>();
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const trimmedMessage = message.trim();
    if (trimmedMessage) {
      onSend(trimmedMessage);
      setMessage('');
      if (isTyping) {
        onTyping(false);
        setIsTyping(false);
      }
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const { value } = e.target;
    setMessage(value);
    
    if (!isTyping && value) {
      onTyping(true);
      setIsTyping(true);
    }
    
    // if (typingTimeout.current) {
    //   clearTimeout(typingTimeout.current);
    // }
    
    // typingTimeout.current = setTimeout(() => {
    //   if (isTyping) {
    //     onTyping(false);
    //     setIsTyping(false);
    //   }
    // }, 3000);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  const handleFileClick = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file && onFileUpload) {
      onFileUpload(file);
    }
    if (e.target) {
      e.target.value = '';
    }
  };

  return (
    <form onSubmit={handleSubmit} className="border-t border-border bg-background p-4">
      <div className="flex items-end gap-2">
        <div className="flex-1 relative">
          <div className="flex items-center gap-2">
            <button
              type="button"
              onClick={handleFileClick}
              disabled={disabled}
              className="p-2 text-muted-foreground hover:text-foreground transition-colors disabled:opacity-50"
              aria-label="Attach file"
            >
              <Paperclip className="w-5 h-5" />
              <input
                type="file"
                ref={fileInputRef}
                onChange={handleFileChange}
                className="hidden"
                disabled={disabled}
                accept="image/*,.pdf,.doc,.docx,.xls,.xlsx"
              />
            </button>
            <button
              type="button"
              className="p-2 text-muted-foreground hover:text-foreground transition-colors"
              aria-label="Add emoji"
            >
              <Smile className="w-5 h-5" />
            </button>
          </div>
          <div className="mt-2">
            <textarea
              value={message}
              onChange={handleChange}
              onKeyDown={handleKeyDown}
              placeholder={placeholder}
              disabled={disabled}
              className="w-full min-h-[40px] max-h-32 px-4 py-2 pr-12 bg-background border border-input rounded-full focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent resize-none"
              rows={1}
              style={{ minHeight: '40px' }}
            />
          </div>
        </div>
        <Button
          type="submit"
          size="icon"
          className="rounded-full w-10 h-10 flex-shrink-0"
          disabled={!message.trim() || disabled}
          aria-label="Send message"
        >
          <Send className="w-4 h-4" />
        </Button>
      </div>
    </form>
  );
}
