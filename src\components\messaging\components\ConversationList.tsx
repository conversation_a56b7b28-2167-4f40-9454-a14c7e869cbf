'use client';

import { Conversation } from '../types';
import { cn } from '@/lib/utils';
import { formatDistanceToNow } from 'date-fns';
import Image from 'next/image';
import { Badge } from '@/components/ui/Badge';
import { Skeleton } from '@/components/ui/Skeleton';

export interface ConversationListProps {
  conversations: Conversation[];
  selectedConversationId?: string;
  onSelectConversation: (id: string) => void;
  currentUserId: string;
  loading?: boolean;
  emptyState?: React.ReactNode;
}

export function ConversationList({
  conversations,
  selectedConversationId,
  onSelectConversation,
  currentUserId,
  loading = false,
  emptyState,
}: ConversationListProps) {
  const getOtherUser = (conversation: Conversation) => {
    return conversation.participants.find(p => p.id !== currentUserId) || conversation.participants[0];
  };

  if (loading) {
    return (
      <div className="space-y-4 p-4">
        {[...Array(5)].map((_, i) => (
          <div key={i} className="flex items-center space-x-3 p-3 rounded-lg">
            <Skeleton className="h-12 w-12 rounded-full" />
            <div className="flex-1">
              <Skeleton className="h-4 w-3/4 mb-2" />
              <Skeleton className="h-3 w-full" />
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (conversations.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-full p-6 text-center text-muted-foreground">
        {emptyState || (
          <>
            <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center mb-4">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="32"
                height="32"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="text-muted-foreground"
              >
                <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z" />
              </svg>
            </div>
            <h3 className="text-lg font-medium mb-1">No conversations yet</h3>
            <p className="text-sm">Start a conversation to see it here</p>
          </>
        )}
      </div>
    );
  }

  return (
    <div className="divide-y divide-border overflow-y-auto">
      {conversations.map((conversation) => {
        const otherUser = getOtherUser(conversation);
        const isActive = selectedConversationId === conversation.id;
        const lastMessage = conversation.lastMessage;
        
        return (
          <button
            key={conversation.id}
            className={cn(
              'w-full text-left p-4 hover:bg-muted/50 transition-colors',
              isActive && 'bg-muted/30',
              conversation.unreadCount > 0 && 'bg-primary/5'
            )}
            onClick={() => onSelectConversation(conversation.id)}
          >
            <div className="flex items-start justify-between">
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between">
                  <h3 className="font-medium text-foreground truncate">
                    {otherUser?.name || 'Unknown User'}
                  </h3>
                  {lastMessage?.timestamp && (
                    <span className="text-xs text-muted-foreground whitespace-nowrap ml-2">
                      {formatDistanceToNow(new Date(lastMessage.timestamp), { addSuffix: true })}
                    </span>
                  )}
                </div>
                
                <div className="flex items-center mt-1">
                  {/* <p className="text-sm text-muted-foreground truncate flex-1">
                    {getLastMessagePreview(lastMessage)}
                  </p> */}
                  
                  {conversation.unreadCount > 0 && (
                    <Badge variant="default" className="ml-2 flex-shrink-0">
                      {conversation.unreadCount > 9 ? '9+' : conversation.unreadCount}
                    </Badge>
                  )}
                </div>
              </div>
              
              <div className="ml-3 flex-shrink-0">
                <div className="relative">
                  <div className="w-10 h-10 rounded-full bg-muted-foreground/10 flex items-center justify-center overflow-hidden">
                    {otherUser?.avatar ? (
                      <Image
                        src={otherUser.avatar}
                        alt={otherUser.name || 'User avatar'}
                        width={40}
                        height={40}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <span className="text-lg font-medium text-muted-foreground">
                        {otherUser?.name?.charAt(0).toUpperCase() || '?'}
                      </span>
                    )}
                  </div>
                  {otherUser?.isOnline && (
                    <span className="absolute bottom-0 right-0 w-3 h-3 rounded-full bg-green-500 border-2 border-background"></span>
                  )}
                </div>
              </div>
            </div>
          </button>
        );
      })}
    </div>
  );
}
