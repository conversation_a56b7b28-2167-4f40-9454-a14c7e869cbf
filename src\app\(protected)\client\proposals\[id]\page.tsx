"use client";
 

import { useState, useEffect } from "react";
import Image from 'next/image';
import { useParams, useRouter } from "next/navigation";
import { useAuth } from "@/lib/auth/AuthContext";
import { jobService } from "@/api/jobService";
import { Button } from "@/components/ui";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/Card";
import { Icon } from "@/components/ui";
import { Badge } from "@/components/ui/Badge";
import { format, formatDistanceToNow } from "date-fns";
import Link from "next/link";
import { toast } from "react-hot-toast";

interface ProposalDetails {
  id: string;
  jobId: string;
  freelancerId: string;
  coverLetter: string;
  bidAmount: number;
  proposedRate: number;
  status: string;
  estimatedDelivery?: string;
  createdAt: string;
  updatedAt: string;
  job?: {
    id: string;
    title: string;
    description: string;
    budget: number;
    status: string;
    isRemote: boolean;
    clientId: string;
    createdAt: string;
    client?: {
      id: string;
      name: string;
      email: string;
    };
  };
  freelancer?: {
    id: string;
    name: string;
    email: string;
    profilePicture?: string;
    title?: string;
    bio?: string;
    location?: string;
    skills?: string[];
  };
}

const ProposalDetailsPage = () => {
  const params = useParams();
  const router = useRouter();
  const { isAuthenticated, user } = useAuth();
  const [proposal, setProposal] = useState<ProposalDetails | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const proposalId = params.id as string;

  useEffect(() => {
    const fetchProposalDetails = async () => {
      if (!user?.username || !proposalId) return;

      try {
        setIsLoading(true);
        setError(null);

        // Get all proposals for the client and find the specific one
        const jobs = await jobService.listJobs({ clientId: user.username });
        let targetProposal: ProposalDetails | undefined;
        
        // Search through all jobs to find the matching proposal
        for (const job of jobs.items || []) {
          const proposals = await jobService.getJobProposals(job.id);
          const found = proposals.find(p => p.id === proposalId);
          if (found) {
            targetProposal = {
              ...found,
              job: {
                ...job,
                client: {
                  id: user.username,
                  name: user.attributes?.name || user.attributes?.email || 'Client',
                  email: user.attributes?.email || ''
                }
              },
              freelancer: found.freelancer
            } as ProposalDetails;
            break;
          }
        }

        if (!targetProposal) {
          setError('Proposal not found');
          return;
        }

        setProposal(targetProposal);
      } catch (err) {
        console.error('Error fetching proposal details:', err);
        setError('Failed to load proposal details');
      } finally {
        setIsLoading(false);
      }
    };

    if (isAuthenticated && user?.attributes?.['custom:role'] === 'CLIENT') {
      fetchProposalDetails();
    }
  }, [isAuthenticated, user, proposalId]);

  const getStatusBadge = (status: string) => {
    type StatusConfig = {
      variant: 'secondary' | 'success' | 'destructive';
      label: string;
      icon: string;
    };

    const statusConfig: Record<string, StatusConfig> = {
      PENDING: { variant: 'secondary', label: 'Pending', icon: 'Clock' },
      ACCEPTED: { variant: 'success', label: 'Accepted', icon: 'CheckCircle' },
      REJECTED: { variant: 'destructive', label: 'Rejected', icon: 'XCircle' },
    };

    const config = statusConfig[status] || { 
      variant: 'secondary' as const, 
      label: status, 
      icon: 'HelpCircle' 
    };

    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        <Icon name={config.icon as any} size="sm" />
        {config.label}
      </Badge>
    );
  };

  const handleAcceptProposal = async () => {
    if (!proposal) return;
    
    try {
      await jobService.updateProposalStatus(proposal.id, 'ACCEPTED');
      toast.success('Proposal accepted successfully');
      // Refresh the page to show updated status
      router.refresh();
    } catch (error) {
      console.error('Error accepting proposal:', error);
      toast.error('Failed to accept proposal');
    }
  };

  const handleRejectProposal = async () => {
    if (!proposal) return;
    
    try {
      await jobService.updateProposalStatus(proposal.id, 'REJECTED');
      toast.success('Proposal rejected');
      // Refresh the page to show updated status
      router.refresh();
    } catch (error) {
      console.error('Error rejecting proposal:', error);
      toast.error('Failed to reject proposal');
    }
  };

  if (!isAuthenticated) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Icon name="Loader2" size="xl" className="animate-spin" />
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button variant="ghost" onClick={() => router.back()}>
            <Icon name="ArrowLeft" size="sm" className="mr-2" />
            Back
          </Button>
        </div>
        <div className="flex items-center justify-center py-12">
          <Icon name="Loader2" size="xl" className="animate-spin" />
        </div>
      </div>
    );
  }

  if (error || !proposal) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button variant="ghost" onClick={() => router.back()}>
            <Icon name="ArrowLeft" size="sm" className="mr-2" />
            Back
          </Button>
        </div>
        <Card>
          <CardContent className="py-12 text-center">
            <Icon name="AlertCircle" size="xl" className="mx-auto mb-4 text-muted-foreground" />
            <h3 className="text-lg font-semibold mb-2">
              {error || 'Proposal not found'}
            </h3>
            <p className="text-muted-foreground mb-4">
              The proposal you&#39;re looking for doesn&#39;t exist or you don&#39;t have permission to view it.
            </p>
            <Button asChild>
              <Link href="/client/proposals">
                View All Proposals
              </Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" onClick={() => router.back()}>
            <Icon name="ArrowLeft" size="sm" className="mr-2" />
            Back to Proposals
          </Button>
        </div>
        <div>
          <h1 className="text-2xl font-bold">Proposal Details</h1>
          <p className="text-muted-foreground">
            Submitted {formatDistanceToNow(new Date(proposal.createdAt), { addSuffix: true })}
          </p>
        </div>
        {getStatusBadge(proposal.status)}
      </div>

      {/* Job Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Icon name="Briefcase" size="md" />
            Job Information
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h3 className="font-semibold text-lg mb-2">
              {proposal.job?.title || 'Job Title Not Available'}
            </h3>
            <p className="text-muted-foreground">
              {proposal.job?.description || 'No description available'}
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <p className="text-sm text-muted-foreground">Budget</p>
              <p className="font-medium">${proposal.job?.budget?.toFixed(2)}</p>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Client</p>
              <p className="font-medium">
                {proposal.job?.client?.name || 'Client information not available'}
              </p>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Status</p>
              <Badge variant={proposal.job?.status === 'OPEN' ? 'success' : 'secondary'}>
                {proposal.job?.status || 'UNKNOWN'}
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Proposal Details */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Icon name="FileText" size="md" />
                <span>Proposal Details</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <p className="text-sm text-muted-foreground">Cover Letter</p>
                <div className="mt-1 p-4 bg-muted/50 rounded-lg">
                  {proposal.coverLetter || 'No cover letter provided'}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-muted-foreground">Bid Amount</p>
                  <p className="font-medium">${proposal.bidAmount?.toFixed(2)}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Proposed Rate</p>
                  <p className="font-medium">${proposal.proposedRate?.toFixed(2)}/hour</p>
                </div>
                {proposal.estimatedDelivery && (
                  <div>
                    <p className="text-sm text-muted-foreground">Estimated Delivery</p>
                    <p className="font-medium">
                      {new Date(proposal.estimatedDelivery).toLocaleDateString()}
                    </p>
                  </div>
                )}
                <div>
                  <p className="text-sm text-muted-foreground">Status</p>
                  {getStatusBadge(proposal.status)}
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Icon name="Briefcase" size="md" />
                <span>Job Details</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h3 className="text-lg font-semibold">{proposal.job?.title}</h3>
                <p className="text-muted-foreground">
                  Posted {formatDistanceToNow(new Date(proposal.job?.createdAt || ''), { addSuffix: true })}
                </p>
              </div>
              
              <div>
                <p className="text-sm text-muted-foreground">Description</p>
                <p className="mt-1">{proposal.job?.description || 'No description provided'}</p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-muted-foreground">Budget</p>
                  <p className="font-medium">${proposal.job?.budget?.toFixed(2)}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Location</p>
                  <p className="font-medium">
                    {proposal.job?.isRemote ? 'Remote' : 'On-site'}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Icon name="User" size="md" />
                <span>Freelancer</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center gap-4">
                <div className="h-16 w-16 rounded-full bg-muted flex items-center justify-center overflow-hidden relative">
                  {proposal.freelancer?.profilePicture ? (
                    <Image
                      src={proposal.freelancer.profilePicture}
                      alt={proposal.freelancer.name || 'Freelancer'}
                      fill
                      className="object-cover"
                      sizes="64px"
                    />
                  ) : (
                    <Icon name="User" className="h-6 w-6 text-primary" />
                  )}
                </div>
                <div>
                  <p className="font-medium">{proposal.freelancer?.name || 'Unknown Freelancer'}</p>
                  <p className="text-sm text-muted-foreground">
                    {proposal.freelancer?.email || 'No email provided'}
                  </p>
                </div>
              </div>

              {proposal.freelancer?.title && (
                <div>
                  <p className="text-sm text-muted-foreground">Title</p>
                  <p className="font-medium">{proposal.freelancer.title}</p>
                </div>
              )}

              {proposal.freelancer?.location && (
                <div>
                  <p className="text-sm text-muted-foreground">Location</p>
                  <p className="font-medium">{proposal.freelancer.location}</p>
                </div>
              )}

              {proposal.freelancer?.bio && (
                <div>
                  <p className="text-sm text-muted-foreground">Bio</p>
                  <p className="text-muted-foreground mt-1">{proposal.freelancer.bio}</p>
                </div>
              )}

              {proposal.freelancer?.skills && proposal.freelancer.skills.length > 0 && (
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Skills</p>
                  <div className="flex flex-wrap gap-2 mt-2">
                    {proposal.freelancer.skills.map((skill) => (
                      <Badge key={skill} variant="secondary">
                        {skill}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
            
            <div className="border-t p-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">
                  Submitted {formatDistanceToNow(new Date(proposal.createdAt), { addSuffix: true })}
                </span>
              </div>
            </div>
          </Card>

          {proposal.status === 'PENDING' && (
            <div className="flex gap-2">
              <Button 
                variant="outline" 
                onClick={() => router.back()}
                className="flex-1"
              >
                Back to Proposals
              </Button>
              <Button 
                variant="destructive"
                onClick={() => handleRejectProposal()}
                className="flex-1"
              >
                Reject
              </Button>
              <Button 
                onClick={() => handleAcceptProposal()}
                className="flex-1"
              >
                Accept
              </Button>
            </div>
          )}

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Bid Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4">
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Bid Amount</span>
                    <span className="font-medium">${proposal.bidAmount?.toFixed(2)}</span>
                  </div>
                  {proposal.proposedRate && (
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Proposed Rate</span>
                      <span className="font-medium">${proposal.proposedRate}/hr</span>
                    </div>
                  )}
                  <div className="flex justify-between pt-2 border-t">
                    <span className="text-muted-foreground">Status</span>
                    {getStatusBadge(proposal.status)}
                  </div>
                </div>

                <div className="pt-4 space-y-2 border-t">
                  <p className="text-sm text-muted-foreground">Submitted</p>
                  <p className="text-sm">
                    {format(new Date(proposal.createdAt), 'MMM d, yyyy')}
                  </p>
                  {proposal.updatedAt && proposal.updatedAt !== proposal.createdAt && (
                    <p className="text-xs text-muted-foreground">
                      Last updated: {format(new Date(proposal.updatedAt), 'MMM d, yyyy')}
                    </p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}

export default ProposalDetailsPage;
